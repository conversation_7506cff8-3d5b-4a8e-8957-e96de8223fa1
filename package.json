{"name": "gestao-tarifaria", "version": "1.0.0", "main": "index.js", "repository": "<EMAIL>:v3/becomex/PORTAIS/GESTAO-TARIFARIA", "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"postinstall": "./postinstall.sh", "build": "NODE_ENV=production parcel build assets/vuejs/src/*.js -d assets/vuejs/dist/ --no-minify --no-source-maps", "watch": "parcel watch assets/vuejs/src/*.js -d assets/vuejs/dist/ --no-source-maps", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"axios": "^0.21.1", "bootstrap": "3.4.1", "ckeditor4": "^4.16.2", "handlebars": "^4.7.7", "jquery": "^3.5.1", "jquery-ui-dist": "^1.12.1", "lodash": "^4.17.21", "moment": "^2.29.1", "node-sass": "^7.0.3", "parcel": "1.12.3", "regenerator-runtime": "^0.13.7", "vue": "^2.6.12", "vue-hot-reload-api": "^2.3.4", "vue-loading-overlay": "^3.4.2", "vue-select": "^3.11.2", "vuedraggable": "^2.24.3", "vuejs": "^3.0.1", "vuelidate": "^0.7.6"}, "devDependencies": {"@babel/plugin-proposal-logical-assignment-operators": "^7.20.7", "@vue/component-compiler-utils": "^3.2.0", "cssnano": "^5.0.8", "eslint": "^8.56.0", "sass": "^1.89.2", "vue-template-compiler": "^2.6.12"}}