.DS_Store
/repo/application/config/config.php
/repo/application/config/database.php
!/repo/application/logs/index.html
/repo/application/logs/*.*
/repo/assets/fotos/*.*
/repo/assets/fotos/*/*.*
!/repo/assets/fotos/index.html
/repo/assets/tmp/*.*
!/repo/assets/tmp/index.html

/application/config/config.php
/application/config/database.php
/application/config/email.php

application/config/email.php

/application/cache/*.*
!/application/cache/index.html
!/application/cache/.htaccess

/application/logs/*.*
!/application/logs/index.html
/assets/fotos/*.*
/assets/fotos/*/*.*
!/assets/fotos/index.html

/assets/uploads/*.*
/assets/uploads/*/*.*
!/assets/uploads/index.html
/docker-capistrano
.settings/
/assets/tmp/*.*
!/assets/tmp/index.html
/docker-capistrano
/assets/logo_empresa/*.*
!/assets/logo_empresa/index.html

/assets/anexos/*.*
!/assets/anexos/index.html

/assets/base_estatisticas/*.*
!/assets/base_estatisticas/index.html

/assets/perguntas/*
/assets/perguntas/
/assets/perguntas/*.*
/assets/respostas/*.*

/index.php
/.htaccess

/node_modules/
/node_modules
node_modules

.settings/
.project


Gemfile.lock

.env
.cache/
.cache
.cache/*

/assets/perguntas/*
/assets/respostas/*

*.lock
**/*.log

# Logs específicos da cron DIANA
application/logs/cron_diana/*.log

docker-compose.yml
nginx/*
php/*
