.vld-shown {
  overflow: hidden;
}

.vld-overlay {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  align-items: center;
  display: none;
  justify-content: center;
  overflow: hidden;
  z-index: 9999;
}

.vld-overlay.is-active {
  display: flex;
}

.vld-overlay.is-full-page {
  z-index: 9999;
  position: fixed;
}

.vld-overlay .vld-background {
  bottom: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  background: #fff;
  opacity: 0.5;
}

.vld-overlay .vld-icon, .vld-parent {
  position: relative;
}

.header[data-v-acb7c5] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.body[data-v-acb7c5] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  /* padding: 25px 15px; */
}
.icon[data-v-acb7c5] {
  font-size: smaller;
  margin-right: 10px;
}
.text-title[data-v-acb7c5] {
  color: #8d9296;
  font-weight: 600;
}
.text-body[data-v-acb7c5] {
  font-size: 20px;
  font-weight: 500;
  color: #4d545b;
  margin: 0;
}
.footer[data-v-acb7c5] {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  display: flex;
  justify-content: space-between;
}
.list[data-v-acb7c5],
.form-move-selected[data-v-acb7c5] {
  display: flex;
  width: 100%;
  margin: 0 0 10px;
}
.table-movement[data-v-acb7c5] {
  width: 100%;
  border: 1px solid;
  border-radius: 5px;
}
.table-movement td[data-v-acb7c5] {
  background: white;
}
.table-movement th[data-v-acb7c5],
.table-movement td[data-v-acb7c5] {
  padding: 10px;
}
.table-movement-thead[data-v-acb7c5] {
  color: white;
  background-color: #337ab7;
}
.table-movement-tbody[data-v-acb7c5] {
  display: block;
  max-height: 200px;
  overflow-y: auto;
}
.table-movement-thead[data-v-acb7c5],
.table-movement-tbody tr[data-v-acb7c5] {
  display: table;
  width: 100%;
  table-layout: fixed;
}
.odd-item td[data-v-acb7c5] {
  background: #e2e3e5;
}
.move-selected-input[data-v-acb7c5] {
  width: 100%;
}.header[data-v-f3f15e] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.body[data-v-f3f15e] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  /* padding: 25px 15px; */
}
.icon[data-v-f3f15e] {
  font-size: smaller;
  margin-right: 10px;
}
.text-title[data-v-f3f15e] {
  color: #8d9296;
  font-weight: 600;
}
.text-body[data-v-f3f15e] {
  font-size: 20px;
  font-weight: 500;
  color: #4d545b;
  margin: 0;
}
.footer[data-v-f3f15e] {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  display: flex;
  justify-content: space-between;
}
.list[data-v-f3f15e],
.form-fill-in-lot[data-v-f3f15e] {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin: 0 0 10px;
}
.form-fill-in-lot[data-v-f3f15e] {
  gap: 15px;
}
.table-movement[data-v-f3f15e] {
  width: 100%;
  border: 1px solid;
}
.table-movement td[data-v-f3f15e] {
  background: white;
}
.table-movement th[data-v-f3f15e],
.table-movement td[data-v-f3f15e] {
  padding: 10px;
}
.table-movement-thead[data-v-f3f15e] {
  color: white;
  background-color: #337ab7;
}
.table-movement-tbody[data-v-f3f15e] {
  display: block;
  max-height: 200px;
  overflow-y: auto;
}
.table-movement-thead[data-v-f3f15e],
.table-movement-tbody tr[data-v-f3f15e] {
  display: table;
  width: 100%;
  table-layout: fixed;
}
.odd-item td[data-v-f3f15e] {
  background: #e2e3e5;
}
.fill-in-lot-input[data-v-f3f15e] {
  width: 100%;
  border: 1px solid #ddd;
}
.input-container[data-v-f3f15e],
.label-container[data-v-f3f15e] {
  padding: 10px;
}
.label-container[data-v-f3f15e] {
  background-color: #e2e3e5;
  margin-bottom: 1px solid #ddd;
}
.label-container label[data-v-f3f15e] {
  margin: 0;
}
.input-container[data-v-f3f15e] {
  /* border-radius: 0 0 5px 5px; */
}.card-history[data-v-355fa3] {
  border: 1px solid #ddd;
  border-radius: 5px;
  margin: 10px 0;
}
.card-history-header[data-v-355fa3] {
  background-color: #e2e3e5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 5px 5px 0 0;
  padding: 10px;
}
.card-history-header h4[data-v-355fa3],
.card-history-header h5[data-v-355fa3] {
  margin: 0;
}
.card-history-header h4[data-v-355fa3] {
  font-weight: 700;
}
.card-history-body[data-v-355fa3] {
  padding: 10px;
}
.strong[data-v-355fa3] {
  font-weight: 700;
}.radio-container[data-v-2dc0cb] {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}
.custom-radio[data-v-2dc0cb]:checked {
  accent-color: #007bff;
}
.card-container[data-v-2dc0cb] {
  border: 1px solid #ddd;
  padding: 10px;
  box-shadow: rgba(0, 0, 0, 0.15) 1.95px 1.95px 2.6px;
  border-radius: 5px;
}
.form-check-inline[data-v-2dc0cb] {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.form-check-inline input[data-v-2dc0cb] {
  margin: 0;
}
.radio-label-fw-normal[data-v-2dc0cb] {
  font-weight: 400;
}
.form-check-label[data-v-2dc0cb] {
  margin: 0;
}
.form-input[data-v-2dc0cb] {
  position: relative;
  margin-bottom: 10px;
}
.error[data-v-2dc0cb] {
  position: absolute;
  bottom: -18px;
  color: red;
  font-size: 0.9em;
}.header[data-v-327396] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.body[data-v-327396] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 25px 15px;
}
.icon[data-v-327396] {
  font-size: smaller;
  margin-right: 10px;
}
.text-title[data-v-327396] {
  color: #8d9296;
  font-weight: 600;
}
.text-body[data-v-327396] {
  font-size: 20px;
  font-weight: 500;
  color: #4d545b;
  margin: 0;
}
.footer[data-v-327396] {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  display: flex;
  justify-content: space-between;
}
.modal-content-custom[data-v-327396] {
  width: 100%;
}
.btn-danger[data-v-327396] {
  background-color: #b00302 !important;
}
.tab-pane[data-v-327396] {
  padding: 10px;
}
.cursor-pointer[data-v-327396] {
  cursor: pointer;
}
@media (min-width: 992px) {
.modal-lg[data-v-327396] {
    width: 80%;
}
}
@media (min-width: 1500px) {
.modal-lg[data-v-327396] {
    width: 50%;
}
}.header[data-v-3812fc] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.body[data-v-3812fc] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  /* padding: 25px 15px; */
}
.icon[data-v-3812fc] {
  font-size: smaller;
  margin-right: 10px;
}
.text-title[data-v-3812fc] {
  color: #8d9296;
  font-weight: 600;
}
.text-body[data-v-3812fc] {
  font-size: 20px;
  font-weight: 500;
  color: #4d545b;
  margin: 0;
}
.footer[data-v-3812fc] {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  display: flex;
  justify-content: flex-end;
}
.list[data-v-3812fc],
.form-modal[data-v-3812fc] {
  display: flex;
  width: 100%;
  margin: 0 0 10px;
}
.form-modal[data-v-3812fc] {
  flex-direction: column;
}
.table-movement[data-v-3812fc] {
  width: 100%;
  border: 1px solid;
  border-radius: 5px;
}
.table-movement td[data-v-3812fc] {
  background: white;
}
.table-movement th[data-v-3812fc],
.table-movement td[data-v-3812fc] {
  padding: 10px;
}
.table-movement-thead[data-v-3812fc] {
  color: white;
  background-color: #337ab7;
}
.table-movement-tbody[data-v-3812fc] {
  display: block;
  max-height: 200px;
  overflow-y: auto;
}
.table-movement-thead[data-v-3812fc],
.table-movement-tbody tr[data-v-3812fc] {
  display: table;
  width: 100%;
  table-layout: fixed;
}
.odd-item td[data-v-3812fc] {
  background: #e2e3e5;
}
.move-selected-input[data-v-3812fc] {
  width: 100%;
}
.radio-container[data-v-3812fc] {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}
.custom-radio[data-v-3812fc]:checked {
  accent-color: #007bff;
}
.form-check-inline[data-v-3812fc] {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.form-check-inline input[data-v-3812fc] {
  margin: 0;
}
.radio-label-fw-normal[data-v-3812fc] {
  font-weight: 400;
}
.form-check-label[data-v-3812fc] {
  margin: 0;
}
.form-input[data-v-3812fc] {
  position: relative;
}
.error[data-v-3812fc] {
  position: absolute;
  bottom: -18px;
  color: red;
  font-size: 0.9em;
}.color-scale[data-v-7d4092] {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}
.color-segment[data-v-7d4092] {
  width: 100%;
}.tooltip-container[data-v-0c6001] {
  position: relative;
  display: inline-block;
}
.tooltiptext[data-v-0c6001] {
  visibility: visible;
  background-color: black;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px 0;
  width: 250px;
}
.tooltip-container .tooltiptext[data-v-0c6001]::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: black transparent transparent transparent;
}
.tooltip-container:hover .tooltiptext[data-v-0c6001] {
  visibility: visible;
}button[data-v-4d5eab]:focus {
  outline: none;
  box-shadow: none;
}
.table-responsive[data-v-4d5eab] {
  overflow-x: auto;
}
.table-atribute[data-v-4d5eab] {
  padding: 0;
  overflow-x: auto;
  min-height: 250px;
  background-color: #eaeef2;
}
.tooltip-trigger[data-v-4d5eab] {
  display: inline-block;
  position: relative;
}
.tooltip[data-v-4d5eab] {
  position: absolute !important;
  z-index: 9999 !important;
  pointer-events: none;
}
.tooltip-inner[data-v-4d5eab] {
  max-width: 300px;
  background-color: rgba(0, 0, 0, 0.85) !important;
  color: yellow !important;
  padding: 8px 12px !important;
  border-radius: 4px !important;
  text-align: left !important;
}
.table[data-v-4d5eab] {
  margin: 0 !important;
  border-spacing: 0;
  border-collapse: separate;
}
.table thead[data-v-4d5eab] {
  background: #337ab7;
  color: white;
}
.odd-item tr td[data-v-4d5eab] {
  background: #f5f8fb;
}
.table-atribute th[data-v-4d5eab] {
  background: #337ab7;
}
.table-atribute td[data-v-4d5eab] {
  background: white;
}
.table-atribute th[data-v-4d5eab],
.table-atribute td[data-v-4d5eab] {
  border: none;
  vertical-align: middle !important;
}
.table-atribute tr th[data-v-4d5eab]:nth-child(1),
.table-atribute tr td[data-v-4d5eab]:nth-child(1) {
  position: sticky;
  left: 0px;
  width: 50px;
  min-width: 50px;
  max-width: 50px;
  z-index: 1000;
  border-left: 1px solid #ddd;
}
.table-atribute th[data-v-4d5eab]:nth-child(2),
.table-atribute td[data-v-4d5eab]:nth-child(2) {
  position: sticky;
  left: 50px;
  width: 150px;
  min-width: 150px;
  max-width: 150px;
  z-index: 1000;
}
.table-atribute th[data-v-4d5eab]:nth-child(3),
.table-atribute td[data-v-4d5eab]:nth-child(3) {
  position: sticky;
  left: 200px;
  width: 230px;
  min-width: 230px;
  max-width: 230px;
  z-index: 1000;
}
.table-atribute th[data-v-4d5eab]:nth-child(4),
.table-atribute td[data-v-4d5eab]:nth-child(4) {
  position: sticky;
  left: 430px; 
  width: 30px;  
  min-width: 30px;
  max-width: 30px; 
  z-index: 1000;  
  background-color: #337ab7;
  border-right: 1px solid #ddd;
}
.table-atribute td[data-v-4d5eab]:nth-child(4) {
   background-color: white;
}
.odd-item tr td[data-v-4d5eab]:nth-child(4) {
  background: #f5f8fb;
}
.table-atribute th[data-v-4d5eab]:nth-child(5),
.table-atribute td[data-v-4d5eab]:nth-child(5) {
  border-left: 1px solid #ddd;
}
.sticky-actions[data-v-4d5eab] {
  position: sticky;
  right: 0;
  background-color: white;
  z-index: 1000;
  border-left: 1px solid #ddd !important;
  border-right: 1px solid #ddd !important;
  padding: 0 15px;
}
.dynamic-column[data-v-4d5eab] {
  background-color: #296292 !important;
  min-width: 200px;
  width: 200px;
}
.dynamic-column-th[data-v-4d5eab] {
  display: flex;
  align-items: center;
  gap: 10px;
}
.dynamic-column-description[data-v-4d5eab] {
  background-color: #337ab7 !important;
}
.dynamic-column-actions[data-v-4d5eab] {
  background-color: #337ab7 !important;
}
.dynamic-column-actions-th[data-v-4d5eab] {
  display: flex;
  align-items: center;
  gap: 10px;
}
.col-description[data-v-4d5eab] {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.centralize[data-v-4d5eab] {
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-icon[data-v-4d5eab] {
  background-color: white;
  color: black;
  padding: 8px;
  border: 1px solid #cccccc;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-icon[data-v-4d5eab]:focus {
  outline: none;
  box-shadow: none;
}
.btn-icon-fill-lot[data-v-4d5eab] {
  background-color: #337ab7;
  color: white;
  padding: 4px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.btn-icon-fill-lot[data-v-4d5eab]:focus {
  outline: none;
  box-shadow: none;
}
hr[data-v-4d5eab] {
  margin: 0px 0 5px 0;
  border-top: 1px solid #000;
}
.complete-description[data-v-4d5eab] {
  margin: 0 0 10px 0 !important;
}
.btn-load-more[data-v-4d5eab] {
  background-color: white;
  border: 1px solid #cccccc;
  font-weight: 600;
  font-size: 16px;
}
.btn-load-more[data-v-4d5eab]:hover {
  background-color: rgba(0, 0, 0, 0.1);
}
.buttons-actions-container[data-v-4d5eab] {
  display: flex;
  justify-content: space-between;
}
.buttons-actions-container div[data-v-4d5eab] {
  display: flex;
  gap: 15px;
}
.form-check-input[data-v-4d5eab] {
  accent-color: #007bff;
}

/* Estilos do tooltip */
.tooltip[data-v-4d5eab] {
  position: absolute !important;
  z-index: 9999 !important;
  pointer-events: none;
  max-width: 300px;
}
.tooltip-inner[data-v-4d5eab] {
  background-color: rgba(0, 0, 0, 0.85) !important;
  color: yellow !important;
  padding: 8px 12px !important;
  border-radius: 4px !important;
  white-space: normal !important;
  text-align: left !important;
}
.tooltip-arrow[data-v-4d5eab] {
  display: none;
}.header[data-v-fe8467] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.body[data-v-fe8467] {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 25px 15px;
}
.icon[data-v-fe8467] {
  font-size: smaller;
  margin-right: 10px;
}
.text-title[data-v-fe8467] {
  color: #8d9296;
  font-weight: 600;
}
.text-body[data-v-fe8467] {
  font-size: 20px;
  font-weight: 500;
  color: #4d545b;
  margin: 0;
}
.footer[data-v-fe8467] {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  display: flex;
  justify-content: space-between;
}
.modal-dialog-centered[data-v-fe8467] {
  height: 100vh;
  overflow-y: hidden;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-content-custom[data-v-fe8467] {
  width: 100%;
}
.btn-success[data-v-fe8467] {
  background-color: #1d8856 !important;
}
.btn-danger[data-v-fe8467] {
  background-color: #b00302 !important;
}@charset "UTF-8";
.header[data-v-87dda9] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.body[data-v-87dda9] {
  padding: 20px;
  background-color: #f8f9fa;
}
.text-title[data-v-87dda9] {
  color: #8d9296;
  font-weight: 600;
  margin: 0;
}
.footer[data-v-87dda9] {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  display: flex;
  justify-content: flex-end;
}
.icon[data-v-87dda9] {
  font-size: smaller;
  margin-right: 10px;
}

/* Resumo da seleção */
.selection-summary[data-v-87dda9] {
  margin-bottom: 25px;
}
.summary-card[data-v-87dda9] {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.summary-item[data-v-87dda9] {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
.summary-label[data-v-87dda9] {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 5px;
}
.summary-value[data-v-87dda9] {
  font-size: 24px;
  font-weight: bold;
  color: #337ab7;
}

/* Lista de NCMs */
.ncms-list[data-v-87dda9] {
  margin-bottom: 25px;
}
.ncms-list h5[data-v-87dda9] {
  color: #495057;
  margin-bottom: 15px;
  font-weight: 600;
}
.ncms-grid[data-v-87dda9] {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 10px;
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
}
.ncm-item[data-v-87dda9] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}
.ncm-code[data-v-87dda9] {
  font-weight: 600;
  color: #495057;
}
.ncm-count[data-v-87dda9] {
  font-size: 12px;
  color: #6c757d;
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
}

/* Opções de homologação */
.homologation-options[data-v-87dda9] {
  margin-bottom: 25px;
}
.homologation-options h5[data-v-87dda9] {
  color: #495057;
  margin-bottom: 15px;
  font-weight: 600;
}
.radio-container[data-v-87dda9] {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
}
.custom-radio[data-v-87dda9]:checked {
  accent-color: #007bff;
}
.form-check-inline[data-v-87dda9] {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}
.form-check-inline input[data-v-87dda9] {
  margin: 0;
}
.radio-label-fw-normal[data-v-87dda9] {
  font-weight: 400;
}
.form-check-label[data-v-87dda9] {
  margin: 0;
}

/* Campo de justificativa */
.form-input[data-v-87dda9] {
  margin-bottom: 25px;
  position: relative;
}
.form-input label[data-v-87dda9] {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}
.form-control[data-v-87dda9] {
  width: 100%;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 10px;
  font-size: 14px;
}
.error[data-v-87dda9] {
  position: absolute;
  bottom: -18px;
  color: #dc3545;
  font-size: 0.9em;
}

/* Avisos */
.warnings[data-v-87dda9] {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 15px;
}
.warning-item[data-v-87dda9] {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 10px;
  color: #856404;
  font-size: 14px;
}
.warning-item[data-v-87dda9]:last-child {
  margin-bottom: 0;
}
.warning-item i[data-v-87dda9] {
  margin-top: 2px;
  color: #f39c12;
}

/* Spinner para processamento */
.spinning[data-v-87dda9] {
  animation: spin-data-v-87dda9 1s linear infinite;
}
@keyframes spin-data-v-87dda9 {
from {
    transform: rotate(0deg);
}
to {
    transform: rotate(360deg);
}
}
/* Responsividade */
@media (max-width: 768px) {
.summary-card[data-v-87dda9] {
    flex-direction: column;
    gap: 15px;
}
.ncms-grid[data-v-87dda9] {
    grid-template-columns: 1fr;
}
.radio-container[data-v-87dda9] {
    flex-direction: column;
    gap: 10px;
}
}
/* Estilos específicos para o modal Bootstrap */
.modal-content-custom[data-v-87dda9] {
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}
.modal-dialog-centered[data-v-87dda9] {
  display: flex;
  align-items: center;
  justify-content: center; /* align horizontally */
  margin: 0 auto; /* auto margins to center */
  min-height: calc(100% - 1rem);
}

/* Ajustes para botões */
.btn[data-v-87dda9] {
  margin-left: 10px;
}
.btn[data-v-87dda9]:first-child {
  margin-left: 0;
}

/* Scrollbar customizada para a grid de NCMs */
.ncms-grid[data-v-87dda9]::-webkit-scrollbar {
  width: 6px;
}
.ncms-grid[data-v-87dda9]::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}
.ncms-grid[data-v-87dda9]::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}
.ncms-grid[data-v-87dda9]::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}/* Estilos existentes */
.card-header[data-v-c64f3e] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  border: 1px solid #e2e3e5;
  margin: 10px 0;
  padding: 10px;
&:hover {
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
}
&.selected {
    background: #337ab7;
    color: white;
}
}
.btn-link[data-v-c64f3e] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  text-decoration: none;
  color: inherit;
&:focus {
    outline: none;
    box-shadow: none;
}
}
.ncm-title-container[data-v-c64f3e],
.ncm-arrow-container[data-v-c64f3e] {
  display: flex;
  gap: 20px;
  align-items: center;
}
.ncm-title[data-v-c64f3e] {
  font-size: 18px;
  font-weight: 500;
}
.circle[data-v-c64f3e] {
  display: inline-block;
  width: 10px;
  height: 10px;
  background-color: transparent;
  border-radius: 50%;
  border: 1px solid #337ab7;
&.selected {
    border: 1px solid white;
}
}
.blue-circle[data-v-c64f3e] {
  background-color: #337ab7;
&.selected {
    background-color: white;
}
}

/* Novos estilos para seleção em massa */
.bulk-selection-header[data-v-c64f3e] {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  /* background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 5px; */
  margin: 10px 0;
}
.select-all-container[data-v-c64f3e] {
  display: flex;
  align-items: center;
  gap: 10px;
}
.select-all-label[data-v-c64f3e] {
  margin: auto;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
}
.bulk-homologate-btn[data-v-c64f3e] {
  background-color: #337ab7;
  border-color: #2e6da4;
&:hover {
    background-color: #286090;
    border-color: #204d74;
}
&:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
}
}
.ncm-checkbox-container[data-v-c64f3e] {
  display: flex;
  align-items: center;
  margin-right: 15px;
  z-index: 10;
}
.ncm-checkbox[data-v-c64f3e] {
  margin: 0;
  cursor: pointer;
}
.bulk-selection-footer[data-v-c64f3e] {
  position: fixed;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #3276b1;
  color: white;
  padding: 10px 25px;
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 6px 6px 0 0;
  white-space: nowrap;
  transition: bottom 0.2s;
}
.bulk-selection-footer.above-main-footer[data-v-c64f3e] {
  bottom: 50px;
  /* altura do footer do layout */
}
.footer-content[data-v-c64f3e] {
  display: flex;
  align-items: center;
  gap: 20px;
  font-size: 14px;
}
.selection-summary[data-v-c64f3e] {
  font-weight: 500;
}
.footer-actions[data-v-c64f3e] {
  display: flex;
  align-items: center;
  gap: 12px;
}
.footer-action-link[data-v-c64f3e] {
  color: white;
  text-decoration: underline;
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
&:hover {
    color: #e9ecef;
    text-decoration: none;
}
&.loading {
    opacity: 0.7;
    cursor: wait;
}
}
.text-muted[data-v-c64f3e] {
  color: #6c757d !important;
}
.small[data-v-c64f3e] {
  font-size: 0.875em;
}
.separator[data-v-c64f3e] {
  color: #e9ecef;
  font-weight: 300;
}

/* Ajuste para o conteúdo principal quando o footer está visível */
#accordion[data-v-c64f3e] {
  margin-bottom: 80px;
}

/* Input check box específico dessa tela, para retirar o margin que o bootstrap aplica */
.form-check-input[data-v-c64f3e] {
  margin: 0;
}

/* Estilo para checkbox indeterminado */
.form-check-input[data-v-c64f3e]:indeterminate {
  background-color: #007bff;
  border-color: #007bff;
}