<template>
  <div 
    class="bulk-selection-footer" 
    v-if="selectedItems.length > 0 && canShow"
    :class="{ 'above-main-footer': offsetMainFooter }"
  >
    <div class="footer-content">
      <span class="selection-summary">
        Você selecionou: {{ totalSelectedItems }} {{ itemLabel }}(ns).
      </span>
      <div class="footer-actions">
        <a 
          @click="handleSelectAll" 
          class="footer-action-link" 
          :class="{ 'loading': isLoading }"
          v-if="showSelectAllAction"
        >
          {{ isLoading ? 'Carregando...' : `Selecionar todos os ${totalGeneralItems} ${itemLabel}(ns)` }}
          <span v-if="hasLoadedAll && !isLoading" class="small">
            ({{ totalAvailableCount }} {{ countLabel }})
          </span>
        </a>
        <span class="separator" v-if="showSelectAllAction">|</span>
        <a @click="handleClearSelection" class="footer-action-link">
          Limpar seleção
        </a>
        <span class="separator" v-if="showCustomActions && $slots.customActions">|</span>
        <slot name="customActions"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BulkSelectionFooter',
  props: {
    // Itens selecionados (array de IDs ou objetos)
    selectedItems: {
      type: Array,
      required: true,
      default: () => []
    },
    
    // Total de itens selecionados (calculado)
    totalSelectedItems: {
      type: Number,
      required: true,
      default: 0
    },
    
    // Total de itens disponíveis para seleção
    totalGeneralItems: {
      type: Number,
      required: true,
      default: 0
    },
    
    // Total de registros disponíveis (ex: NCMs)
    totalAvailableCount: {
      type: Number,
      default: 0
    },
    
    // Se pode mostrar o footer
    canShow: {
      type: Boolean,
      default: true
    },
    
    // Se está carregando dados
    isLoading: {
      type: Boolean,
      default: false
    },
    
    // Se já carregou todos os dados
    hasLoadedAll: {
      type: Boolean,
      default: false
    },
    
    // Se deve mostrar a ação "Selecionar todos"
    showSelectAllAction: {
      type: Boolean,
      default: true
    },
    
    // Se deve mostrar ações customizadas
    showCustomActions: {
      type: Boolean,
      default: false
    },
    
    // Label para os itens (ex: "item", "produto", etc.)
    itemLabel: {
      type: String,
      default: 'item'
    },
    
    // Label para a contagem (ex: "NCMs", "categorias", etc.)
    countLabel: {
      type: String,
      default: 'registros'
    },
    
    // Se deve ajustar posição quando footer principal está visível
    offsetMainFooter: {
      type: Boolean,
      default: false
    }
  },
  
  methods: {
    handleSelectAll() {
      this.$emit('selectAll');
    },
    
    handleClearSelection() {
      this.$emit('clearSelection');
    }
  }
};
</script>

<style scoped>
.bulk-selection-footer {
  position: fixed;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #3276b1;
  color: white;
  padding: 10px 25px;
  /* z-index: 1000; */
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 6px 6px 0 0;
  white-space: nowrap;
  transition: bottom 0.2s;
}

.bulk-selection-footer.above-main-footer {
  bottom: 50px; /* altura do footer do layout */
}

.footer-content {
  display: flex;
  align-items: center;
  gap: 20px;
  font-size: 14px;
}

.selection-summary {
  font-weight: 500;
}

.footer-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.footer-action-link {
  color: white;
  text-decoration: underline;
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
}

.footer-action-link:hover {
  color: #e9ecef;
  text-decoration: none;
}

.footer-action-link.loading {
  opacity: 0.7;
  cursor: wait;
}

.separator {
  color: #e9ecef;
  font-weight: 300;
}

.small {
  font-size: 0.875em;
}

/* Responsividade */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
  
  .footer-actions {
    justify-content: center;
  }
  
  .bulk-selection-footer {
    white-space: normal;
    padding: 15px 20px;
  }
}
</style>
