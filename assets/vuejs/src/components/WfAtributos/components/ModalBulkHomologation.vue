<template>
    <div class="modal fade" id="ModalBulkHomologation" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content modal-content-custom">
                <div class="header">
                    <h4 class="modal-title text-title" id="exampleModalLabel">
                        Homologação em Massa - {{ totalSelectedItems }} itens selecionados
                    </h4>
                    <button type="button" class="close" @click="closeModal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>

                <div class="modal-body body">
                    <!-- Resumo da seleção -->
                    <div class="selection-summary">
                        <div class="summary-card">
                            <div class="summary-item">
                                <span class="summary-label">NCMs selecionadas:</span>
                                <span class="summary-value">{{ selectedNcms.length }}</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">Total de itens:</span>
                                <span class="summary-value">{{ totalSelectedItems }}</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-label">NCMs disponíveis:</span>
                                <span class="summary-value">{{ totalAvailableNcms }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Lista de NCMs selecionadas -->
                    <div class="ncms-list">
                        <h5>NCMs que serão processadas:</h5>
                        <div class="ncms-grid">
                            <div v-for="ncm in selectedNcmsData" :key="ncm.ncm_proposto" class="ncm-item">
                                <span class="ncm-code">{{ formatNCM(ncm.ncm_proposto) }}</span>
                                <span class="ncm-count">{{ ncm.total_itens }} itens</span>
                            </div>
                        </div>
                    </div>

                    <!-- Opções de homologação -->
                    <div class="homologation-options">
                        <h5>Opções de homologação:</h5>
                        <div class="radio-container">
                            <div class="form-check form-check-inline">
                                <input v-model="formValues.homolog_response" class="form-check-input custom-radio"
                                    type="radio" name="bulk_homolog_response" id="bulk_aprove" :value="1"
                                    :checked="formValues.homolog_response == 1" />
                                <label class="form-check-label radio-label-fw-normal" for="bulk_aprove">
                                    Aprovar todos
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input v-model="formValues.homolog_response" class="form-check-input custom-radio"
                                    type="radio" name="bulk_homolog_response" id="bulk_disapprove" :value="2"
                                    :checked="formValues.homolog_response == 2" />
                                <label class="form-check-label radio-label-fw-normal" for="bulk_disapprove">
                                    Reprovar todos
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Justificativa para reprovação -->
                    <div v-if="formValues.homolog_response == 2" class="form-input">
                        <label for="bulk_justification">Justificativa para reprovação *</label>
                        <textarea v-model="formValues.justification" name="bulk_justification" class="form-control"
                            rows="3" placeholder="Informe a justificativa para reprovação dos itens..."
                            @blur="validateField('justification')"></textarea>
                        <span v-if="errors.justification" class="error">
                            {{ errors.justification }}
                        </span>
                    </div>

                    <!-- Avisos importantes -->
                    <div class="warnings">
                        <div class="warning-item">
                            <i class="glyphicon glyphicon-info-sign"></i>
                            <span>Apenas itens com atributos obrigatórios preenchidos serão homologados.</span>
                        </div>
                        <div class="warning-item">
                            <i class="glyphicon glyphicon-exclamation-sign"></i>
                            <span>Esta ação não pode ser desfeita. Confirme antes de prosseguir.</span>
                        </div>
                    </div>
                </div>

                <div class="footer">
                    <div>
                        <button type="button" class="btn btn-default" @click="closeModal">
                            Cancelar
                        </button>
                        <button type="button" class="btn btn-success" @click="submit()" :disabled="isProcessing">
                            <i v-if="isProcessing" class="glyphicon glyphicon-refresh spinning"></i>
                            <i v-else class="glyphicon glyphicon-ok icon"></i>
                            {{ isProcessing ? 'Processando...' : 'Confirmar Homologação' }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import axios from 'axios';

export default {
    name: 'ModalBulkHomologation',
    props: {
        selectedNcms: {
            required: true,
            type: Array,
            default: () => []
        },
        selectedIdsItem: {
            required: true,
            type: Array,
            default: () => []
        },
        totalSelectedItems: {
            required: true,
            type: Number,
            default: 0
        },
        totalAvailableNcms: {
            required: true,
            type: Number,
            default: 0
        },
        selectedNcmsData: {
            required: true,
            type: Array,
            default: () => []
        },
        homologarSemObrigatorios: {
            required: false,
            default: Boolean | Number
        }
    },
    data() {
        return {
            formValues: {
                homolog_response: 1,
                justification: '',
            },
            errors: {},
            isProcessing: false,
        };
    },
    methods: {
        formatNCM(ncm) {
            ncm = ncm.replace(/\D/g, '');
            if (ncm.length !== 8) {
                return ncm; // Retorna o original se não for válido
            }
            return ncm.replace(/(\d{4})(\d{2})(\d{2})/, '$1.$2.$3');
        },

        validateField(field) {
            if (
                field == 'justification' &&
                this.formValues.homolog_response == 2 &&
                this.formValues.justification.trim() == ''
            ) {
                this.$set(this.errors, field, 'Este campo é obrigatório para reprovação.');
            } else if (
                field == 'justification' &&
                this.formValues.homolog_response == 2 &&
                this.formValues.justification.trim() != ''
            ) {
                this.$set(this.errors, field, null);
            }
        },
         async submit() {
            // Validar campos obrigatórios
            if (this.formValues.homolog_response == 2) {
                this.validateField('justification');
                if (this.errors.justification) {
                    return;
                }
            }

            // Confirmar ação
            const confirmMessage = this.formValues.homolog_response == 1
                ? `Você está prestes a APROVAR ${this.totalSelectedItems} itens de ${this.selectedNcms.length} NCM(s). Deseja continuar?`
                : `Você está prestes a REPROVAR ${this.totalSelectedItems} itens de ${this.selectedNcms.length} NCM(s). Deseja continuar?`;

            const result = await this.showConfirmation(confirmMessage);
            if (!result) {
                return;
            }

            this.isProcessing = true;

            try {
                // Preparar dados para envio
                const data = {
                    ncms: this.selectedNcms,
                    homolog_response: this.formValues.homolog_response,
                    justification: this.formValues.justification,
                    total_items: this.totalSelectedItems,
                    ids_item: this.selectedIdsItem
                };

                const response = await axios.post('atributos/ajax_set_status_mass', data);

                if (response.data.err === 0) {
                    swal({
                        title: "Processo Concluído!",
                        // Como estilizar a msg para centralizar os marcadores da ul?
                        html: response.data.msg,
                        type: "success",
                    });

                    this.$emit('bulkHomologationComplete', {
                        success: true,
                        processedItems: response.data.itens_sucesso,
                        failedItems: response.data.itens_falha,
                        processedNcms: this.selectedNcms.length
                    });
                } else {
                    throw new Error(response.data.msg || "Ocorreu um erro desconhecido no servidor.");
                }

                this.closeModal();

            } catch (error) {
                console.error('Erro na homologação em massa:', error);
                swal({
                    title: "Erro!",
                    text: error.message || "Ocorreu um erro durante o processamento. Tente novamente.",
                    type: "error",
                });
            } finally {
                this.isProcessing = false;
            }
        },

        showConfirmation(message) {
            return new Promise((resolve) => {
                swal({
                    title: "Confirmar Ação",
                    text: message,
                    type: "warning",
                    confirmButtonText: "Sim, continuar",
                    cancelButtonText: "Cancelar",
                    showConfirmButton: true,
                    showCancelButton: true,
                    allowOutsideClick: false,
                }).then((result) => {
                    resolve(result);
                });
            });
        },

        closeModal() {
            this.$emit('closeModal');
        },
    },
};
</script>

<style lang="scss" scoped>
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #e5e5e5;
}

.body {
    padding: 20px;
    background-color: #f8f9fa;
}

.text-title {
    color: #8d9296;
    font-weight: 600;
    margin: 0;
}

.footer {
    border-top: 1px solid #e5e5e5;
    padding: 15px;
    display: flex;
    justify-content: flex-end;
}

.icon {
    font-size: smaller;
    margin-right: 10px;
}

/* Resumo da seleção */
.selection-summary {
    margin-bottom: 25px;
}

.summary-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    justify-content: space-around;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.summary-label {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 5px;
}

.summary-value {
    font-size: 24px;
    font-weight: bold;
    color: #337ab7;
}

/* Lista de NCMs */
.ncms-list {
    margin-bottom: 25px;
}

.ncms-list h5 {
    color: #495057;
    margin-bottom: 15px;
    font-weight: 600;
}

.ncms-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    max-height: 200px;
    overflow-y: auto;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
}

.ncm-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.ncm-code {
    font-weight: 600;
    color: #495057;
}

.ncm-count {
    font-size: 12px;
    color: #6c757d;
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
}

/* Opções de homologação */
.homologation-options {
    margin-bottom: 25px;
}

.homologation-options h5 {
    color: #495057;
    margin-bottom: 15px;
    font-weight: 600;
}

.radio-container {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
}

.custom-radio:checked {
    accent-color: #007bff;
}

.form-check-inline {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.form-check-inline input {
    margin: 0;
}

.radio-label-fw-normal {
    font-weight: 400;
}

.form-check-label {
    margin: 0;
}

/* Campo de justificativa */
.form-input {
    margin-bottom: 25px;
    position: relative;
}

.form-input label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #495057;
}

.form-control {
    width: 100%;
    border: 1px solid #ced4da;
    border-radius: 4px;
    padding: 10px;
    font-size: 14px;
}

.error {
    position: absolute;
    bottom: -18px;
    color: #dc3545;
    font-size: 0.9em;
}

/* Avisos */
.warnings {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
}

.warning-item {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 10px;
    color: #856404;
    font-size: 14px;
}

.warning-item:last-child {
    margin-bottom: 0;
}

.warning-item i {
    margin-top: 2px;
    color: #f39c12;
}

/* Spinner para processamento */
.spinning {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

/* Responsividade */
@media (max-width: 768px) {
    .summary-card {
        flex-direction: column;
        gap: 15px;
    }

    .ncms-grid {
        grid-template-columns: 1fr;
    }

    .radio-container {
        flex-direction: column;
        gap: 10px;
    }
}

/* Estilos específicos para o modal Bootstrap */
.modal-content-custom {
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

.modal-dialog-centered {
    display: flex;
    align-items: center;
    justify-content: center; /* align horizontally */
    margin: 0 auto; /* auto margins to center */
    min-height: calc(100% - 1rem);
}

/* Ajustes para botões */
.btn {
    margin-left: 10px;
}

.btn:first-child {
    margin-left: 0;
}

/* Scrollbar customizada para a grid de NCMs */
.ncms-grid::-webkit-scrollbar {
    width: 6px;
}

.ncms-grid::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.ncms-grid::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.ncms-grid::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>