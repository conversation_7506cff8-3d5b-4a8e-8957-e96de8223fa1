<template>
  <div>
    <!-- Header de seleção em massa -->
    <div class="bulk-selection-header" v-if="data && data.length > 0">
      <div class="select-all-container">
        <input id="select-all" type="checkbox" v-model="selectAllNcms" @change="toggleSelectAllNcms" class="form-check-input"
          :indeterminate="isIndeterminate" />
        <label for="select-all" class="select-all-label">Selecionar todas as NCMs</label>
      </div>

      <button :disabled="selectedNcms.length === 0" @click="openBulkHomologationModal"
        class="btn btn-primary bulk-homologate-btn">
        Homologar selecionados
      </button>
    </div>

    <div id="accordion">
      <div class="card" v-for="(item, index) in data" :key="index">
        <div class="card-header" :class="{ selected: selectedItemId === item.ncm_proposto }"
          @click="expandCollapse(item)">
          <!-- Checkbox individual para cada NCM -->
          <div class="ncm-checkbox-container" @click.stop>
            <input type="checkbox" v-model="selectedNcms" :value="item.ncm_proposto"
              class="form-check-input ncm-checkbox" @change="updateSelectAllState" />
          </div>

          <button class="btn btn-link" data-toggle="collapse" :data-target="'#collapse' + item.ncm_proposto"
            aria-expanded="false" :aria-controls="'collapse' + item.ncm_proposto">
            <div class="ncm-title-container">
              <span class="ncm-title mr-3">{{
                formatNCM(item.ncm_proposto)
                }}</span>
              <span data-toggle="tooltip" data-html="true" style="cursor: help"
                title="Atributos obrigatórios desta NCM">
                <div class="circle blue-circle" :class="{ selected: selectedItemId === item.ncm_proposto }"></div>
                <span>{{ item.total_atributos_obrigatorios }}</span>
              </span>
              <span data-toggle="tooltip" data-html="true" style="cursor: help" title="Atributos opcionais desta NCM">
                <div class="circle" :class="{ selected: selectedItemId === item.ncm_proposto }"></div>
                <span>{{ item.total_atributos_nao_obrigatorios }}</span>
              </span>
            </div>
            <div class="ncm-arrow-container">
              <span>{{ item.total_itens }} itens</span>
              <i class="glyphicon" :class="selectedItemId === item.ncm_proposto
                ? 'glyphicon-chevron-up'
                : 'glyphicon-chevron-down'
                "></i>
            </div>
          </button>
        </div>

        <div v-if="selectedItemId === item.ncm_proposto" class="collapse"
          :class="{ show: selectedItemId === item.ncm_proposto }" :aria-labelledby="'heading' + item.ncm_proposto"
          data-parent="#accordion">
          <div class="card-body" style="position: relative">
            <WfTabelaAtributos v-if="selectedItemId === item.ncm_proposto" :data="selectedItemId"
              :totalqtdItems="item.total_itens" @openModalDiscart="openModalDiscart" :saveData="saveData"
              @dataEdited="handleDataEdited" :homologarSemObrigatorios="homologarSemObrigatorios">
            </WfTabelaAtributos>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer de seleção em massa -->
    <div 
      class="bulk-selection-footer" v-if="selectedNcms.length > 0"
      :class="{ 'above-main-footer': offsetMainFooter }"
    >
      <div class="footer-content">
        <span class="selection-summary">Você selecionou: {{ totalSelectedItems }} item(ns).</span>
        <div class="footer-actions">
          <a @click="selectAllItems" class="footer-action-link">Selecionar todos os {{ totalAvailableItems }}
            item(ns)</a>
          <span class="separator">|</span>
          <a @click="clearSelection" class="footer-action-link">Limpar seleção</a>
        </div>
      </div>
    </div>

    <ModalDiscart v-if="modalNcmId" @closeCollapse="closeCollapse" @salvarItens="salvarItens"
      :selectedItemId="modalNcmId"></ModalDiscart>

    <!-- Modal de Homologação em Massa -->
    <ModalBulkHomologation v-if="showBulkHomologationModal" 
      :selectedNcms="selectedNcms"
      :selectedIdsItem="selectedIdsItem"
      :totalSelectedItems="totalSelectedItems"
      :totalAvailableNcms="data.length"
      :selectedNcmsData="selectedNcmsData"
      :homologarSemObrigatorios="homologarSemObrigatorios"
      @closeModal="closeBulkHomologationModal"
      @bulkHomologationComplete="handleBulkHomologationComplete" 
    />
  </div>
</template>

<script>
import axios from 'axios';
import WfTabelaAtributos from './WfTabelaAtributos.vue';
import ModalDiscart from './components/modalDiscart.vue';
import ModalBulkHomologation from './components/ModalBulkHomologation.vue';

export default {
  components: {
    WfTabelaAtributos,
    ModalDiscart,
    ModalBulkHomologation,
  },
  data() {
    return {
      hasBeenEdited: false,
      selectedItemId: null,
      modalNcmId: null,
      saveData: null,
      // Novos dados para seleção em massa
      selectedNcms: [],
      selectAllNcms: false,
      totalAvailableItems: 0,
      // Modal de homologação em massa
      showBulkHomologationModal: false,
      offsetMainFooter: false
    };
  },
  props: {
    data: {
      required: true,
      default: String,
    },
    homologarSemObrigatorios: {
      required: false,
      default: Boolean | Number
    }
  },
  computed: {
    totalSelectedItems() {
      if (!this.data || this.data.length === 0) return 0;

      return this.data
        .filter(item => this.selectedNcms.includes(item.ncm_proposto))
        .reduce((total, item) => (parseInt(total) + parseInt(item.total_itens)), 0);
    },

    totalAvailableItems() {
      if (!this.data || this.data.length === 0) return 0;

      return this.data.reduce((total, item) => (parseInt(total) + parseInt(item.total_itens)), 0);
    },

    isIndeterminate() {
      return this.selectedNcms.length > 0 && this.selectedNcms.length < this.data.length;
    },

    selectedNcmsData() {
      if (!this.data || this.data.length === 0) return [];

      return this.data.filter(item => this.selectedNcms.includes(item.ncm_proposto));
    },
    selectedIdsItem() {
      return this.selectedNcmsData.flatMap(item => item.idArray);
    },
  },
  mounted() {
    $('[data-toggle="tooltip"]').tooltip();
    this.hasBeenEdited = false;
    this.calculateTotalAvailableItems();

    // Monitorar a visibilidade do footer. Esperar o dom carregar com setTimeout
    setTimeout(() => {
      const footer = window.document.querySelector('footer');
      if (!footer) {
        return;
      }

      const observer = new IntersectionObserver(
        ([entry]) => {
          // entry.isIntersecting == true quando o footer aparece
          this.offsetMainFooter = entry.isIntersecting;
        },
        {
          root: null,          // viewport
          threshold: 0,        // assim que qualquer parte tocar a viewport
        }
      );

      observer.observe(footer);
      this.$once('hook:beforeDestroy', () => observer.disconnect());
    }, 500);

    this.normalizeIdArrays();
  },
  watch: {
    data: {
      handler() {
        this.calculateTotalAvailableItems();
      },
      immediate: true
    }
  },
  methods: {
    calculateTotalAvailableItems() {
      this.totalAvailableItems = this.data.reduce((total, item) => (parseInt(total) + parseInt(item.total_itens)), 0);
    },

    toggleSelectAllNcms() {
      if (this.selectAllNcms) {
        this.selectedNcms = this.data.map(item => item.ncm_proposto);
      } else {
        this.selectedNcms = [];
      }
    },

    updateSelectAllState() {
      if (this.selectedNcms.length === this.data.length) {
        this.selectAllNcms = true;
      } else if (this.selectedNcms.length === 0) {
        this.selectAllNcms = false;
      }
      // O estado indeterminado é controlado pela computed property
    },

    selectAllItems() {
      // Selecionar todos os itens de todas as NCMs
      this.selectedNcms = this.data.map(item => item.ncm_proposto);
      this.selectAllNcms = true;
    },

    clearSelection() {
      this.selectedNcms = [];
      this.selectAllNcms = false;
      this.selectedIdsItem = [];
      this.selectedItemId = null;
      this.modalNcmId = null;
    },

    openBulkHomologationModal() {
      if (this.selectedNcms.length === 0) {
        swal({
          title: "Atenção",
          text: "Selecione pelo menos uma NCM para homologar.",
          type: "warning",
        });
        return;
      }

      this.showBulkHomologationModal = true;

      // Mostrar o modal usando jQuery Bootstrap
      this.$nextTick(() => {
        $('#ModalBulkHomologation').modal('show');
      });
    },

    closeBulkHomologationModal() {
      this.showBulkHomologationModal = false;
      $('#ModalBulkHomologation').modal('hide');
    },

    handleBulkHomologationComplete(result) {
      if (result.success) {
        // Limpar seleção após sucesso
        this.clearSelection();

        // Recarregar dados se necessário
        // this.loadData();

        console.log('Homologação em massa concluída:', result);
      }
    },

    async expandCollapse(item) {
      if (this.selectedItemId === item.ncm_proposto) {
        this.openModalDiscart();
      } else if (
        this.selectedItemId &&
        this.selectedItemId !== item.ncm_proposto
      ) {
        this.openModalDiscart();
        this.modalNcmId = item.ncm_proposto;
        this.expandCollapse(item);
      } else {
        this.selectedItemId = item.ncm_proposto;
        this.modalNcmId = item.ncm_proposto;
      }
    },
    handleDataEdited(value) {  // Método para lidar com o evento
      this.hasBeenEdited = value;
    },
    formatNCM(ncm) {
      ncm = ncm.replace(/\D/g, '');
      if (ncm.length !== 8) {
        throw new Error('O NCM deve ter exatamente 8 dígitos');
      }
      return ncm.replace(/(\d{4})(\d{2})(\d{2})/, '$1.$2.$3');
    },
    openModalDiscart() {

      if (this.hasBeenEdited) {
        $('#ModalDiscart').modal('show');
      } else {
        this.closeCollapse({ item: this.selectedItemId });
      }

    },
    salvarItens() {
      this.saveData = true;
      setTimeout(() => {
        this.closeCollapse({ item: this.selectedItemId });
        this.saveData = false;
      }, 2000);
    },
    closeCollapse({ item }) {
      this.hasBeenEdited = false;
      $('#ModalDiscart').modal('hide');
      if (this.selectedItemId === item) {
        this.selectedItemId = null;
        this.modalNcmId = null;
      } else if (this.selectedItemId && this.selectedItemId !== item) {
        this.selectedItemId = item;
        this.modalNcmId = item;
      }
      this.dadosAtributos = { lista: [], itens: {} };
    },
    normalizeIdArrays() {
      this.data.forEach(item => {
        // se já veio array, mantenha; se veio string JSON, converta
        if (typeof item.id_itens === 'string') {
          try {
            item.idArray = JSON.parse(item.id_itens);
          } catch (e) {
            item.idArray = [];       // Se falhar, inicializa como array vazio
          }
        } else {
          item.idArray = item.id_itens;
        }
      });
    },
  },
};
</script>

<style scoped>
/* Estilos existentes */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  border: 1px solid #e2e3e5;
  margin: 10px 0;
  padding: 10px;

  &:hover {
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  }

  &.selected {
    background: #337ab7;
    color: white;
  }
}

.btn-link {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  text-decoration: none;
  color: inherit;

  &:focus {
    outline: none;
    box-shadow: none;
  }
}

.ncm-title-container,
.ncm-arrow-container {
  display: flex;
  gap: 20px;
  align-items: center;
}

.ncm-title {
  font-size: 18px;
  font-weight: 500;
}

.circle {
  display: inline-block;
  width: 10px;
  height: 10px;
  background-color: transparent;
  border-radius: 50%;
  border: 1px solid #337ab7;

  &.selected {
    border: 1px solid white;
  }
}

.blue-circle {
  background-color: #337ab7;

  &.selected {
    background-color: white;
  }
}

/* Novos estilos para seleção em massa */
.bulk-selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  /* background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 5px; */
  margin: 10px 0;
}

.select-all-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.select-all-label {
  margin: auto;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
}

.bulk-homologate-btn {
  background-color: #337ab7;
  border-color: #2e6da4;

  &:hover {
    background-color: #286090;
    border-color: #204d74;
  }

  &:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
  }
}

.ncm-checkbox-container {
  display: flex;
  align-items: center;
  margin-right: 15px;
  z-index: 10;
}

.ncm-checkbox {
  margin: 0;
  cursor: pointer;
}

.bulk-selection-footer {
  position: fixed;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #3276b1;
  color: white;
  padding: 10px 25px;
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 6px 6px 0 0;
  white-space: nowrap;
  transition: bottom 0.2s;
}

.bulk-selection-footer.above-main-footer {
  bottom: 50px; /* altura do footer do layout */
}

.footer-content {
  display: flex;
  align-items: center;
  gap: 20px;
  font-size: 14px;
}

.selection-summary {
  font-weight: 500;
}

.footer-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.footer-action-link {
  color: white;
  text-decoration: underline;
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;

  &:hover {
    color: #e9ecef;
    text-decoration: none;
  }
}

.separator {
  color: #e9ecef;
  font-weight: 300;
}

/* Ajuste para o conteúdo principal quando o footer está visível */
#accordion {
  margin-bottom: 80px;
}

/* Input check box específico dessa tela, para retirar o margin que o bootstrap aplica */
.form-check-input {
  margin: 0;
}

/* Estilo para checkbox indeterminado */
.form-check-input:indeterminate {
  background-color: #007bff;
  border-color: #007bff;
}
</style>
