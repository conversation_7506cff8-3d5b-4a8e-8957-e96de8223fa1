<template>
  <div>
    <!-- Header de seleção em massa -->
    <div class="bulk-selection-header" v-if="data && data.length > 0 && canHomologar">
      <div class="select-all-container">
        <input id="select-all" type="checkbox" v-model="selectAllNcms" @change="toggleSelectAllNcms"
          class="form-check-input" :indeterminate="isIndeterminate" />
        <label for="select-all" class="select-all-label">
          {{ isLoadingAllNcms ? 'Carregando...' : 'Selecionar todas as NCMs' }}
          <span v-if="hasLoadedAllNcms" class="text-muted small">
            ({{ allNcmsData.length }} NCMs disponíveis)
          </span>
        </label>
      </div>

      <button :disabled="selectedNcms.length === 0" @click="openBulkHomologationModal"
        class="btn btn-primary bulk-homologate-btn">
        Homologar selecionados
      </button>
    </div>

    <div id="accordion">
      <div class="card" v-for="(item, index) in data" :key="index">
        <div class="card-header" :class="{ selected: selectedItemId === item.ncm_proposto }"
          @click="expandCollapse(item)">
          <!-- Checkbox individual para cada NCM -->
          <div class="ncm-checkbox-container" @click.stop v-if="canHomologar">
            <input type="checkbox" v-model="selectedNcms" :value="item.ncm_proposto"
              class="form-check-input ncm-checkbox" @change="updateSelectAllState" />
          </div>

          <button class="btn btn-link" data-toggle="collapse" :data-target="'#collapse' + item.ncm_proposto"
            aria-expanded="false" :aria-controls="'collapse' + item.ncm_proposto">
            <div class="ncm-title-container">
              <span class="ncm-title mr-3">{{
                formatNCM(item.ncm_proposto)
              }}</span>
              <span data-toggle="tooltip" data-html="true" style="cursor: help"
                title="Atributos obrigatórios desta NCM">
                <div class="circle blue-circle" :class="{ selected: selectedItemId === item.ncm_proposto }"></div>
                <span>{{ item.total_atributos_obrigatorios }}</span>
              </span>
              <span data-toggle="tooltip" data-html="true" style="cursor: help" title="Atributos opcionais desta NCM">
                <div class="circle" :class="{ selected: selectedItemId === item.ncm_proposto }"></div>
                <span>{{ item.total_atributos_nao_obrigatorios }}</span>
              </span>
            </div>
            <div class="ncm-arrow-container">
              <span>{{ item.total_itens }} itens</span>
              <i class="glyphicon" :class="selectedItemId === item.ncm_proposto
                ? 'glyphicon-chevron-up'
                : 'glyphicon-chevron-down'
                "></i>
            </div>
          </button>
        </div>

        <div v-if="selectedItemId === item.ncm_proposto" class="collapse"
          :class="{ show: selectedItemId === item.ncm_proposto }" :aria-labelledby="'heading' + item.ncm_proposto"
          data-parent="#accordion">
          <div class="card-body" style="position: relative">
            <WfTabelaAtributos v-if="selectedItemId === item.ncm_proposto" :data="selectedItemId"
              :totalqtdItems="item.total_itens" @openModalDiscart="openModalDiscart" :saveData="saveData"
              @dataEdited="handleDataEdited" :homologarSemObrigatorios="homologarSemObrigatorios">
            </WfTabelaAtributos>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Modal de Homologação em Massa -->
    <ModalBulkHomologation v-if="showBulkHomologationModal"
      :selectedNcms="selectedNcms"
      :selectedIdsItem="selectedIdsItem"
      :totalSelectedItems="totalSelectedItems"
      :totalAvailableNcms="hasLoadedAllNcms ? allNcmsData.length : data.length"
      :selectedNcmsData="selectedNcmsData"
      :homologarSemObrigatorios="homologarSemObrigatorios"
      @closeModal="closeBulkHomologationModal"
      @bulkHomologationComplete="handleBulkHomologationComplete"
    />

    <!-- Footer de seleção em massa -->
    <BulkSelectionFooter
      :selectedItems="selectedNcms"
      :totalSelectedItems="totalSelectedItems"
      :totalGeneralItems="totalGeneralItems"
      :totalAvailableCount="hasLoadedAllNcms ? allNcmsData.length : data.length"
      :canShow="canHomologar"
      :isLoading="isLoadingAllNcms"
      :hasLoadedAll="hasLoadedAllNcms"
      :offsetMainFooter="offsetMainFooter"
      itemLabel="item"
      countLabel="NCMs"
      @selectAll="selectAllItems"
      @clearSelection="clearSelection"
    />

    <ModalDiscart v-if="modalNcmId" 
      :selectedItemId="modalNcmId"
      @closeCollapse="closeCollapse"
      @salvarItens="salvarItens"
    />


  </div>
</template>

<script>
import axios from 'axios';
import WfTabelaAtributos from './WfTabelaAtributos.vue';
import ModalDiscart from './components/modalDiscart.vue';
import ModalBulkHomologation from './components/ModalBulkHomologation.vue';
import BulkSelectionFooter from './components/BulkSelectionFooter.vue';

export default {
  components: {
    WfTabelaAtributos,
    ModalDiscart,
    ModalBulkHomologation,
    BulkSelectionFooter,
  },
  data() {
    return {
      hasBeenEdited: false,
      selectedItemId: null,
      modalNcmId: null,
      saveData: null,
      // Novos dados para seleção em massa
      selectedNcms: [],
      selectAllNcms: false,
      totalAvailableItems: 0,
      // Modal de homologação em massa
      showBulkHomologationModal: false,
      offsetMainFooter: false,
      // Dados para seleção de todos os registros
      allNcmsData: [], // Armazena todos os dados das NCMs (incluindo outras páginas)
      isLoadingAllNcms: false,
      hasLoadedAllNcms: false,
      // Total geral de itens (para exibição no footer)
      totalGeneralItems: 0,
      hasLoadedTotalGeneral: false
    };
  },
  props: {
    data: {
      required: true,
      default: String,
    },
    homologarSemObrigatorios: {
      required: false,
      default: Boolean | Number
    },
    canHomologar: {
      required: false,
      type: Boolean,
      default: true
    }
  },
  computed: {
    totalSelectedItems() {
      // Usa allNcmsData se disponível, senão usa data da página atual
      const dataSource = this.hasLoadedAllNcms ? this.allNcmsData : this.data;
      if (!dataSource || dataSource.length === 0) return 0;

      return dataSource
        .filter(item => this.selectedNcms.includes(item.ncm_proposto))
        .reduce((total, item) => (parseInt(total) + parseInt(item.total_itens)), 0);
    },

    totalAvailableItems() {
      // Se já carregou o total geral, usa ele
      if (this.hasLoadedTotalGeneral) {
        return this.totalGeneralItems;
      }

      // Se carregou todos os dados das NCMs, calcula o total
      if (this.hasLoadedAllNcms) {
        const total = this.allNcmsData.reduce((total, item) => (parseInt(total) + parseInt(item.total_itens)), 0);
        return total;
      }

      // Senão, usa apenas os dados da página atual
      if (!this.data || this.data.length === 0) {
        return 0;
      }

      const total = this.data.reduce((total, item) => (parseInt(total) + parseInt(item.total_itens)), 0);
      return total;
    },

    isIndeterminate() {
      // Usa allNcmsData se disponível, senão usa data da página atual
      const dataSource = this.hasLoadedAllNcms ? this.allNcmsData : this.data;
      if (!dataSource || dataSource.length === 0) return false;

      return this.selectedNcms.length > 0 && this.selectedNcms.length < dataSource.length;
    },

    selectedNcmsData() {
      // Usa allNcmsData se disponível, senão usa data da página atual
      const dataSource = this.hasLoadedAllNcms ? this.allNcmsData : this.data;
      if (!dataSource || dataSource.length === 0) return [];

      return dataSource.filter(item => this.selectedNcms.includes(item.ncm_proposto));
    },

    selectedIdsItem() {
      return this.selectedNcmsData.flatMap(item => item.idArray);
    },
  },
  mounted() {
    $('[data-toggle="tooltip"]').tooltip();
    this.hasBeenEdited = false;
    this.calculateTotalAvailableItems();

    // Carregar o total geral de itens para exibição correta no footer
    this.loadTotalGeneralItems();

    // Fallback: tentar novamente após 2 segundos se não carregou
    setTimeout(() => {
      if (!this.hasLoadedTotalGeneral) {
        this.loadTotalGeneralItems();
      }
    }, 2000);

    // Monitorar a visibilidade do footer. Esperar o dom carregar com setTimeout
    setTimeout(() => {
      const footer = window.document.querySelector('footer');
      if (!footer) {
        return;
      }

      const observer = new IntersectionObserver(
        ([entry]) => {
          // entry.isIntersecting == true quando o footer aparece
          this.offsetMainFooter = entry.isIntersecting;
        },
        {
          root: null,          // viewport
          threshold: 0,        // assim que qualquer parte tocar a viewport
        }
      );

      observer.observe(footer);
      this.$once('hook:beforeDestroy', () => observer.disconnect());
    }, 500);

    this.normalizeIdArrays();
  },
  watch: {
    data: {
      handler() {
        this.calculateTotalAvailableItems();
        // Recarregar o total geral quando os dados mudarem (ex: filtros aplicados)
        this.hasLoadedTotalGeneral = false;
        this.loadTotalGeneralItems();
      },
      immediate: true
    }
  },
  methods: {
    calculateTotalAvailableItems() {
      this.totalAvailableItems = this.data.reduce((total, item) => (parseInt(total) + parseInt(item.total_itens)), 0);
    },

    async toggleSelectAllNcms() {
      if (this.selectAllNcms) {
        // Se não carregou todos os dados ainda, buscar do servidor
        if (!this.hasLoadedAllNcms) {
          await this.loadAllNcmsData();
        }

        // Selecionar todas as NCMs (da página atual ou de todos os dados)
        const dataSource = this.hasLoadedAllNcms ? this.allNcmsData : this.data;
        this.selectedNcms = dataSource.map(item => item.ncm_proposto);
      } else {
        this.selectedNcms = [];
      }
    },

    updateSelectAllState() {
      // Usa allNcmsData se disponível, senão usa data da página atual
      const dataSource = this.hasLoadedAllNcms ? this.allNcmsData : this.data;
      if (!dataSource || dataSource.length === 0) return;

      if (this.selectedNcms.length === dataSource.length) {
        this.selectAllNcms = true;
      } else if (this.selectedNcms.length === 0) {
        this.selectAllNcms = false;
      }
      // O estado indeterminado é controlado pela computed property
    },

    async selectAllItems() {
      // Carregar todos os dados se ainda não foram carregados
      if (!this.hasLoadedAllNcms) {
        await this.loadAllNcmsData();
      }

      // Selecionar todos os itens de todas as NCMs
      const dataSource = this.hasLoadedAllNcms ? this.allNcmsData : this.data;
      this.selectedNcms = dataSource.map(item => item.ncm_proposto);
      this.selectAllNcms = true;
    },

    async loadTotalGeneralItems() {
      if (this.hasLoadedTotalGeneral) {
        return;
      }

      try {
        const response = await axios.get('/wf/atributos/get_all_ncms_for_bulk_selection');

        if (response.data.err === 0) {
          // Calcula o total geral de itens sem armazenar todos os dados
          this.totalGeneralItems = response.data.data.reduce((total, item) => {
            return parseInt(total) + parseInt(item.total_itens);
          }, 0);
          this.hasLoadedTotalGeneral = true;
        } else {
          console.error('Erro na resposta do servidor:', response.data.msg);
        }
      } catch (error) {
        console.error('Erro ao carregar total geral de itens:', error);
        // Em caso de erro, mantém o comportamento atual (total da página)
      }
    },

    async loadAllNcmsData() {
      if (this.isLoadingAllNcms || this.hasLoadedAllNcms) {
        return;
      }

      this.isLoadingAllNcms = true;

      try {
        const response = await axios.get('/wf/atributos/get_all_ncms_for_bulk_selection');

        if (response.data.err === 0) {
          this.allNcmsData = response.data.data;
          this.hasLoadedAllNcms = true;

          // Se ainda não carregou o total geral, aproveita para calcular
          if (!this.hasLoadedTotalGeneral) {
            this.totalGeneralItems = this.allNcmsData.reduce((total, item) => {
              return parseInt(total) + parseInt(item.total_itens);
            }, 0);
            this.hasLoadedTotalGeneral = true;
          }

          // Normalizar os arrays de IDs para os novos dados
          this.normalizeIdArraysForData(this.allNcmsData);
        } else {
          throw new Error(response.data.msg || 'Erro ao carregar dados');
        }
      } catch (error) {
        console.error('Erro ao carregar todas as NCMs:', error);
        swal({
          title: "Erro",
          text: "Não foi possível carregar todos os dados. Usando apenas os dados da página atual.",
          type: "warning",
        });
      } finally {
        this.isLoadingAllNcms = false;
      }
    },

    clearSelection() {
      this.selectedNcms = [];
      this.selectAllNcms = false;
      this.selectedIdsItem = [];
      this.selectedItemId = null;
      this.modalNcmId = null;
    },

    openBulkHomologationModal() {
      if (this.selectedNcms.length === 0) {
        swal({
          title: "Atenção",
          text: "Selecione pelo menos uma NCM para homologar.",
          type: "warning",
        });
        return;
      }

      this.showBulkHomologationModal = true;

      // Mostrar o modal usando jQuery Bootstrap
      this.$nextTick(() => {
        $('#ModalBulkHomologation').modal('show');
      });
    },

    closeBulkHomologationModal() {
      this.showBulkHomologationModal = false;
      $('#ModalBulkHomologation').modal('hide');
    },

    handleBulkHomologationComplete(result) {
      if (result.success) {
        // Limpar seleção após sucesso
        this.clearSelection();

        // Recarregar dados se necessário
        // this.loadData();
      }
    },

    async expandCollapse(item) {
      if (this.selectedItemId === item.ncm_proposto) {
        this.openModalDiscart();
      } else if (
        this.selectedItemId &&
        this.selectedItemId !== item.ncm_proposto
      ) {
        this.openModalDiscart();
        this.modalNcmId = item.ncm_proposto;
        this.expandCollapse(item);
      } else {
        this.selectedItemId = item.ncm_proposto;
        this.modalNcmId = item.ncm_proposto;
      }
    },
    handleDataEdited(value) {  // Método para lidar com o evento
      this.hasBeenEdited = value;
    },
    formatNCM(ncm) {
      ncm = ncm.replace(/\D/g, '');
      if (ncm.length !== 8) {
        throw new Error('O NCM deve ter exatamente 8 dígitos');
      }
      return ncm.replace(/(\d{4})(\d{2})(\d{2})/, '$1.$2.$3');
    },
    openModalDiscart() {

      if (this.hasBeenEdited) {
        $('#ModalDiscart').modal('show');
      } else {
        this.closeCollapse({ item: this.selectedItemId });
      }

    },
    salvarItens() {
      this.saveData = true;
      setTimeout(() => {
        this.closeCollapse({ item: this.selectedItemId });
        this.saveData = false;
      }, 2000);
    },
    closeCollapse({ item }) {
      this.hasBeenEdited = false;
      $('#ModalDiscart').modal('hide');
      if (this.selectedItemId === item) {
        this.selectedItemId = null;
        this.modalNcmId = null;
      } else if (this.selectedItemId && this.selectedItemId !== item) {
        this.selectedItemId = item;
        this.modalNcmId = item;
      }
      this.dadosAtributos = { lista: [], itens: {} };
    },
    normalizeIdArrays() {
      this.normalizeIdArraysForData(this.data);
    },

    normalizeIdArraysForData(dataArray) {
      if (!dataArray || !Array.isArray(dataArray)) return;

      dataArray.forEach(item => {
        // se já veio array, mantenha; se veio string JSON, converta
        if (typeof item.id_itens === 'string') {
          try {
            item.idArray = JSON.parse(item.id_itens);
          } catch (e) {
            item.idArray = [];
          }
        } else {
          item.idArray = item.id_itens;
        }
      });
    },
  },
};
</script>

<style scoped>
/* Estilos existentes */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  border: 1px solid #e2e3e5;
  margin: 10px 0;
  padding: 10px;

  &:hover {
    box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  }

  &.selected {
    background: #337ab7;
    color: white;
  }
}

.btn-link {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  text-decoration: none;
  color: inherit;

  &:focus {
    outline: none;
    box-shadow: none;
  }
}

.ncm-title-container,
.ncm-arrow-container {
  display: flex;
  gap: 20px;
  align-items: center;
}

.ncm-title {
  font-size: 18px;
  font-weight: 500;
}

.circle {
  display: inline-block;
  width: 10px;
  height: 10px;
  background-color: transparent;
  border-radius: 50%;
  border: 1px solid #337ab7;

  &.selected {
    border: 1px solid white;
  }
}

.blue-circle {
  background-color: #337ab7;

  &.selected {
    background-color: white;
  }
}

/* Novos estilos para seleção em massa */
.bulk-selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  /* background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 5px; */
  margin: 10px 0;
}

.select-all-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.select-all-label {
  margin: auto;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
}

.bulk-homologate-btn {
  background-color: #337ab7;
  border-color: #2e6da4;

  &:hover {
    background-color: #286090;
    border-color: #204d74;
  }

  &:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
  }
}

.ncm-checkbox-container {
  display: flex;
  align-items: center;
  margin-right: 15px;
  z-index: 10;
}

.ncm-checkbox {
  margin: 0;
  cursor: pointer;
}

/* Estilos do footer movidos para BulkSelectionFooter.vue */

/* Ajuste para o conteúdo principal quando o footer está visível */
#accordion {
  margin-bottom: 80px;
}

/* Input check box específico dessa tela, para retirar o margin que o bootstrap aplica */
.form-check-input {
  margin: 0;
}

/* Estilo para checkbox indeterminado */
.form-check-input:indeterminate {
  background-color: #007bff;
  border-color: #007bff;
}
</style>
