<?php

defined('BASEPATH') || exit('No direct script access allowed');
// application/controllers/cli/worker.php
require_once APPPATH . 'exceptions/EmailQueueException.php';

/**
 * @property CI_Input $input
 * @property CI_Loader $load
 * @property Email_queue_model $email_queue_model
 * @property CadItemWfAtributoService $caditemwfatributoservice
 */
class Worker extends CI_Controller
{
    /**
     * @var array Mapeia os tipos de trabalho para seus respectivos métodos manipuladores (handlers).
     */
    private $handlers = [
        'alteracao_status_massa' => 'handle_alteracao_status_massa',
        'alteracao_status_by_ncm' => 'handle_alteracao_status_by_ncm',
        // Adicione novos tipos e seus handlers aqui no futuro.
        // 'novo_usuario_cadastrado' => 'handle_novo_usuario',
    ];

    public function __construct()
    {
        parent::__construct();
        if (!$this->input->is_cli_request()) {
            show_error('Este script só pode ser executado via linha de comando.', 403);
            exit;
        }
        $this->load->model('email_queue_model');
        $this->load->service('CadItemWfAtributoService');
    }

    /**
     * Processa a fila de e-mails, atuando como um despachante (dispatcher).
     *
     * @param int $limit O número máximo de e-mails a serem processados nesta execução.
     */
    public function process_email_queue($limit = 10)
    {
        echo "Iniciando processamento da fila de e-mails...\n";
        $pending_emails = $this->email_queue_model->get_pending_emails($limit);

        if (empty($pending_emails)) {
            echo "Nenhum e-mail pendente para processar.\n";
            return;
        }

        foreach ($pending_emails as $job) {
            $this->email_queue_model->mark_as_processing($job->id);
            echo "Processando job #{$job->id}...\n";

            try {
                $payload = json_decode($job->payload, true);
                if (empty($payload) || !isset($payload['type'])) {
                    throw new EmailQueueException('O tipo de e-mail (type) não foi definido no payload.');
                }

                $type = $payload['type'];

                // Verifica se existe um handler mapeado para o tipo de job
                if (!isset($this->handlers[$type])) {
                    throw new EmailQueueException("Tipo de e-mail desconhecido: {$type}");
                }

                $handler_method = $this->handlers[$type];

                // Verifica se o método do handler realmente existe na classe
                if (!method_exists($this, $handler_method)) {
                    throw new EmailQueueException("Manipulador '{$handler_method}' não encontrado para o tipo: {$type}");
                }

                // Chama o método do handler dinamicamente
                $this->$handler_method($payload);

                $this->email_queue_model->mark_as_sent($job->id);

                echo "Job #{$job->id} concluído com sucesso.\n";
            } catch (EmailQueueException $e) {
                $this->email_queue_model->mark_as_failed($job->id, $e->getMessage());
                echo "Falha no job #{$job->id}: {$e->getMessage()}\n";
            } catch (Exception $e) {
                $this->email_queue_model->mark_as_failed($job->id, 'Erro inesperado: ' . $e->getMessage());
                echo "Erro inesperado no job #{$job->id}: {$e->getMessage()}\n";
            }
        }
        echo "Processamento da fila concluído.\n";
    }

    /**
     * Manipulador para o e-mail de alteração de status em massa.
     * @param array $payload Os dados necessários para o envio do e-mail.
     */
    private function handle_alteracao_status_massa($payload)
    {
        if (!isset($payload['item_ids']) ||
            !isset($payload['id_empresa']) ||
            !isset($payload['status_id'])) {
            throw new EmailQueueException('Payload inválido para o tipo alteracao_status_massa.');
        }

        $this->caditemwfatributoservice->enviarAlteracaoStatusMassa(
            $payload['item_ids'],
            $payload['id_empresa'],
            $payload['status_id']
        );
    }

    /**
     * Manipulador para o e-mail de alteração de status por NCM.
     * @param array $payload Os dados necessários para o envio do e-mail.
     */
    private function handle_alteracao_status_by_ncm($payload)
    {
        if (!isset($payload['ncm']) || !isset($payload['id_empresa']) || !isset($payload['status_id'])) {
            throw new EmailQueueException('Payload inválido para o tipo alteracao_status_by_ncm.');
        }

        $this->caditemwfatributoservice->enviarAlteracaoStatusMassa(
            $payload['item_ids'],
            $payload['id_empresa'],
            $payload['status_id']
        );
    }
    // Adicione novos manipuladores conforme necessário.
}
