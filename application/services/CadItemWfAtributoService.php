<?php
// application/services/CadItemWfAtributoService.php

defined('BASEPATH') || exit('No direct script access allowed');

/**
 * @property CI_Loader $load
 * @property Cad_item_wf_atributo_model $cad_item_wf_atributo_model
 */
class CadItemWfAtributoService
{
    protected $CI;

    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->model('cad_item_wf_atributo_model');
    }

    /**
     * Envia e-mail de alteração de status em massa para itens.
     * @param array $item_ids
     * @param int $id_empresa
     * @param int $status_id
     * @throws Exception
     */
    public function enviarAlteracaoStatusMassa($item_ids, $id_empresa, $status_id)
    {
        $items = $this->CI
            ->cad_item_wf_atributo_model
            ->get_itens_by_ids($item_ids);
        
        $part_numbers = array_column($items, 'part_number');
        $estabelecimentos = array_column($items, 'estabelecimento');

        $this->CI
            ->cad_item_wf_atributo_model
            ->send_mail_alteracao_status_item_wf_atributos(
                $part_numbers,
                $estabelecimentos,
                $id_empresa,
                $status_id
            );
    }
}
