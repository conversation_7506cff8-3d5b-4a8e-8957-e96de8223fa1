<html>

<head>
    <title>Becomex - Gestão Tarifária</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
</head>

<body bgcolor="#fcfcfc" leftmargin="0" topmargin="0">
    <table width="97%" border="0" cellpadding="0" cellspacing="0" align="center" style="border: 1px solid #f0f0e1; box-shadow: 0px 5px 10px #f1f1f1">
        <tr>
            <td bgcolor="#dddddd">
                <table border="0" cellpadding="30" cellspacing="0">
                    <tr>
                        <td>
                            <a href="<?php echo config_item('online_url') ?>" target="_blank">
                                <img border="0" src="<?php echo config_item('online_url')  ?>/assets/img/header/logo.png" alt="Logo" />
                            </a>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000">

                <div style="margin: 30px 2px 0 6px; font-size: 13px; color: #575757">
                    <h3>Alteração de status de item </h3>
                    <br>
                </div>
                <div style="margin: 30px 2px 0 6px; font-size: 15px; color: #575757">
                    <p>
                        Olá,
                    </p>
                    <p>
                        Um item o qual você é responsável, sofreu alteração de status:
                    </p>
                </div>
                <br>
            </td>
        </tr>
        <br>
        <tr>
            <td>
                <table width="100%" border="1" cellpadding="20" cellspacing="0">
                    <tr>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; background: #DDDDDD; text-align:center">
                            <strong>Estab</strong>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; background: #DDDDDD; text-align:center;">
                            <strong>Part Number</strong>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; background: #DDDDDD; text-align:center">
                            <strong>Descrição</strong>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; background: #DDDDDD; text-align:center">
                            <strong>Owner</strong>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; background: #DDDDDD; text-align:center">
                            <strong>Data de cadastro</strong>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; background: #DDDDDD; text-align:center">
                            <strong>Status anterior</strong>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; background: #DDDDDD; text-align:center">
                            <strong>Status atual</strong>
                        </td>
                    </tr>
                    <tr>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; font-size: 12px">
                            <?php echo $item->estabelecimento; ?>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; font-size: 12px">
                            <?php echo $item->part_number ?>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; font-size: 12px">
                            <?php if ($hasDescricaoGlobal) : ?>
                                <?php echo (!empty($item->descricao)) ? $item->descricao : 
                                $item->descricao_global . "<strong> (GLOBAL)</strong>"  ?>
                            <?php else : ?>
                                <?php echo $item->descricao; ?>
                            <?php endif; ?>
                        </td>
                        <?php
                        $responsavel_nome = '';
                        foreach ($owners as $owner) {
                            if (isset($owner->responsavel_gestor) && $owner->responsavel_gestor == 1) {
                                $responsavel_nome = $owner->nome;
                                break;
                            }
                        }
                        ?>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; font-size: 12px">
                            <?php if (!empty($owners)): ?>
                                <?php echo $owners[0]->codigo ?> - <?php echo $owners[0]->descricao ?> -
                                <?php if (isset($responsavel_nome)) : ?>
                                    <?php echo $responsavel_nome; ?>
                                <?php endif; ?>
                            <?php endif; ?>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; font-size: 12px">
                            <?php echo $item->dat_criacao; ?>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; font-size: 12px">
                            <?php echo $status_anterior; ?>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; font-size: 12px">
                            <?php echo $status_atual; ?>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

    </table>
</body>

</html>