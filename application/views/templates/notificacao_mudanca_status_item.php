<html>

<head>
    <title>Becomex - Gestão Tarifária</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
</head>

<body bgcolor="#fcfcfc" leftmargin="0" topmargin="0">
    <table width="97%" border="0" cellpadding="0" cellspacing="0" align="center" style="border: 1px solid #f0f0e1; box-shadow: 0px 5px 10px #f1f1f1">
        <tr>
            <td bgcolor="#dddddd">
                <table border="0" cellpadding="30" cellspacing="0">
                    <tr>
                        <td>
                            <a href="<?php echo config_item('online_url') ?>" target="_blank">
                                <img border="0" src="<?php echo config_item('online_url')  ?>/assets/img/header/logo.png" alt="Logo" />
                            </a>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000">

                <div style="margin: 30px 2px 0 6px; font-size: 13px; color: #575757">
                    <h3>Alteração de status de item </h3>
                    <br>
                </div>
                <div style="margin: 30px 2px 0 6px; font-size: 15px; color: #575757">
                    <p>
                        Olá,
                    </p>
                    <p>
                        Um item o qual você é usuário seguidor sofreu alteração de status:
                    </p>
                </div>
                <br>
            </td>
        </tr>
        <br>
        <tr>
            <td>
                <table width="100%" border="1" cellpadding="20" cellspacing="0">
                    <tr>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; background: #DDDDDD; text-align:center">
                            <strong>Estab</strong>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; background: #DDDDDD; text-align:center;">
                            <strong>Part Number</strong>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; background: #DDDDDD; text-align:center">
                            <strong>Descrição</strong>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; background: #DDDDDD; text-align:center">
                            <strong>Owner</strong>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; background: #DDDDDD; text-align:center">
                            <strong>Data de cadastro</strong>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; background: #DDDDDD; text-align:center">
                            <strong>Status anterior</strong>
                        </td>
                        <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; background: #DDDDDD; text-align:center">
                            <strong>Status atual</strong>
                        </td>
                    </tr>
                        <tr>
                            <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; font-size: 12px">
                                <?php echo $item->estabelecimento; ?>
                            </td>
                            <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; font-size: 12px">
                                <?php echo $item->part_number ?>
                            </td>
                            <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; font-size: 12px">
                                <?php echo $item->descricao; ?>
                            </td>
                            <?php
                            $responsavel_nome = '';
                            foreach ($owners as $owner) {
                                if (isset($owner->responsavel_gestor) && $owner->responsavel_gestor == 1) {
                                    $responsavel_nome = $owner->nome;
                                    break;
                                }
                            }
                            ?>
                                <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; font-size: 12px">
                                    <?php if (!empty($owners)): ?>
                                        <?php echo $owners[0]->codigo ?> - <?php echo $owners[0]->descricao ?> -
                                        <?php if (isset($responsavel_nome)): ?>
                                            <?php echo $responsavel_nome; ?>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </td>
                            <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; font-size: 12px">
                                <?php echo $item->dat_criacao; ?>
                            </td>
                            <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; font-size: 12px">
                                <?php echo $status_anterior; ?>
                            </td>
                            <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000; font-size: 12px">
                                <?php echo $status_atual; ?>
                            </td>
                        </tr>
                </table>
            </td>
        </tr>
        <!-- <tr>
            <td>
                <?php foreach ($part_numbers as $part_number => $item) : ?>
                    <table align="left" width="100%" border="0" cellspacing="0" cellpadding="10" bgcolor="#f1f1f1">
                        <hr style="margin-top: 15px;margin-bottom: 0;border: 1px solid #dee2e6;border-bottom: none;">
                        <tr bgcolor="#f9f9f9">
                            <th align="left" style="width:30%; font-size:14px;font-family:Ubuntu,Arial,sans-serif; color: #8e8e8e">Part Number: </th>
                            <td style="font-size:14px;font-family:Ubuntu,Arial,sans-serif; color: #8e8e8e"><?php echo $part_number ?></td>
                        </tr>
                        <tr bgcolor="#f9f9f9">
                            <th align="left" style="width:30%; font-size:14px;font-family:Ubuntu,Arial,sans-serif; color: #8e8e8e">Descrição: </th>
                            <td style="font-size:14px;font-family:Ubuntu,Arial,sans-serif; color: #8e8e8e"><?php echo $item['descricao']; ?></td>
                        </tr>
                    </table>
                    <table width="100%" border="0" cellpadding="30" cellspacing="0">
                        <?php if (isset($item['message_status']) && !empty($item['message_status'])) : ?>
                            <tr align="center">
                                <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000">

                                    <div style="margin: 0 2px; font-size: 13px; color: #575746">
                                        <span style="font-size: 18px; color: #428bca">
                                            <?php echo 'Alteração do Status do Item'; ?>
                                        </span>
                                        <br />
                                        <p>
                                            <?php echo reset($item['message_status']); ?>
                                        </p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                        <?php if (isset($item['message_responsavel_fiscal']) && !empty($item['message_responsavel_fiscal'])) : ?>
                            <tr align="center">
                                <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000">

                                    <div style="margin: 0 2px; font-size: 13px; color: #575746">
                                        <span style="font-size: 18px; color: #428bca">
                                            <?php echo 'Alteração do Responsável Fiscal'; ?>
                                        </span>
                                        <br />
                                        <p>
                                            <?php echo reset($item['message_responsavel_fiscal']); ?>
                                        </p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                        <?php if (isset($item['message_responsavel_engenharia']) && !empty($item['message_responsavel_engenharia'])) : ?>
                            <tr align="center">
                                <td style="font-family: helvetica neue, helvetica, Arial; line-height: 20px; color: #000">

                                    <div style="margin: 0 2px; font-size: 13px; color: #575746">
                                        <span style="font-size: 18px; color: #428bca">
                                            <?php echo 'Alteração do Responsável de Engenharia'; ?>
                                        </span>
                                        <br />
                                        <p>
                                            <?php echo reset($item['message_responsavel_engenharia']); ?>
                                        </p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </table>
                <?php endforeach; ?>
                <table width="100%" border="0" cellpadding="10" cellspacing="0" bgcolor="#464637">
                    <tr>
                        <td>
                            <p style="font-family: Arial; font-size: 10px; color: #fff; margin: 0 20px; line-height: 14px;">
                                Copyright &copy; <?php echo date("Y") ?> <a href="//becomex.com.br/" target="_blank" style="color: #fff; text-decoration: none;">Becomex</a> &ndash; Todos os direitos reservados.
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr> -->
    </table>
</body>

</html>