<?php

class Log_Wf_Atributos_Model extends MY_Model
{    
    public $_table = 'log_wf_atributos';

    public function __construct()
    {
        parent::__construct();
    }

    public function registrar_log(
                        $id_item,
                        $part_numbers,
                        $estabelecimentos,
                        $id_empresa,
                        $status_novo,
                        $tipo,
                        $id_usuario,
                        $justificativa
                    )
    {
        if (!empty($id_item))
        {
            if (!empty($id_item) && !empty($id_item[0]['ncm_proposto']))
            {
                $ids = array_map(function($item) {
                    return $item['id_item'];
                },$id_item);
                $id_item = $ids;
            }
 
            if (!is_array($id_item))
            {
                $id_item = array($id_item);
            }

            $this->db->join('cad_item ci', 'ci.part_number = i.part_number AND ci.estabelecimento = i.estabelecimento AND ci.id_empresa = i.id_empresa', 'inner');
            $this->db->where_in('ci.id_item', $id_item);
            $query = $this->db->get('item i');
            $result = $query->result();
            $where = '';
            if (!empty($result))
            {
                foreach ($result as $r)
                {
                    $where .= "OR (i.part_number = '{$r->part_number}' AND i.estabelecimento = '{$r->estabelecimento}' AND i.id_empresa = {$r->id_empresa})";
                }
            }

        } else {

            $where = '';
            if (is_array($part_numbers) && is_array($estabelecimentos)) {
                foreach($part_numbers as $x => $part_number){
                    $where .= "OR (i.part_number = '{$part_number}' AND i.estabelecimento = '{$estabelecimentos[$x]}' AND i.id_empresa = {$id_empresa})";
                }
            } else {
                $where .= "OR (i.part_number = '{$part_numbers}' AND i.estabelecimento = '{$estabelecimentos}' AND i.id_empresa = {$id_empresa})";
            }
        }
     
        $where = substr($where, 2);

        $this->db->query("INSERT INTO log_wf_atributos
                                            (
                                                part_number,
                                                estabelecimento,
                                                id_empresa,
                                                status_atual,
                                                justificativa,
                                                informacoes_integracao,
                                                id_usuario,
                                                tipo,
                                                criado_em,
                                                nao_atualiza_item
                                            )SELECT 
                                                i.part_number,
                                                i.estabelecimento,
                                                i.id_empresa,
                                                (SELECT status FROM status_wf_atributos WHERE id = '{$status_novo}') as status_atual,
                                                '{$justificativa}',
                                                NULL,
                                                '{$id_usuario}',
                                                '{$tipo}',
                                                NOW(),
                                                1
                                            FROM item i
                                            WHERE {$where}");

        $this->db->query("UPDATE item i
                            INNER JOIN cad_item ON i.part_number = cad_item.part_number 
                                AND i.estabelecimento = cad_item.estabelecimento 
                                AND i.id_empresa = cad_item.id_empresa
                            INNER JOIN empresa e ON e.id_empresa = i.id_empresa
                            SET i.data_modificacao = NOW()
                            WHERE {$where}");
        return true;

    }

    /*
        TODO: Foi adicionado o nao_atualiza_item = 1, porém
        não foi adicionado o foi atualizado o valor do data_modificacao
        Essa função não está sendo utilizada atualmente
    */
    public function registrar_log_cron($tipo, $id_usuario, $justificativa)
    {
        $this->db->query("INSERT INTO log_wf_atributos
                                (
                                    part_number, 
                                    estabelecimento, 
                                    id_empresa,
                                    status_atual, 
                                    justificativa, 
                                    informacoes_integracao, 
                                    id_usuario,
                                    tipo,
                                    criado_em,
                                    nao_atualiza_item
                                )SELECT 
                                    i.part_number,
                                    i.estabelecimento, 
                                    i.id_empresa,
                                    (SELECT status FROM status_wf_atributos WHERE id = '2') as status_atual,
                                    '{$justificativa}', 
                                    NULL,
                                    '1',
                                    '{$tipo}',
                                    NOW(),
                                    1
                                FROM item i
                                INNER JOIN cad_item ci ON i.part_number = ci.part_number 
                                    AND i.estabelecimento = ci.estabelecimento 
                                    AND i.id_empresa = ci.id_empresa
                                WHERE i.wf_status_atributos IS NULL
                                    AND i.wf_status_integracao IS NULL");
        return true;

    }
}