<?php

class Email_queue_model extends CI_Model
{
    const TABLE_NAME = 'email_queue';
    const MAX_ATTEMPTS = 3;
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SENT = 'sent';
    const STATUS_FAILED = 'failed';

    public function __construct()
    {
        parent::__construct();
    }

    public function add_to_queue($payload)
    {
        $data = [
            'payload' => json_encode($payload),
            'status' => self::STATUS_PENDING,
        ];
        $this->db->insert(self::TABLE_NAME, $data);
        return $this->db->insert_id();
    }

    public function get_pending_emails($limit = 10)
    {
        $this->db->where('status', self::STATUS_PENDING);
        $this->db->where('attempts <', self::MAX_ATTEMPTS);
        $this->db->order_by('created_at', 'ASC');
        $this->db->limit($limit);
        $query = $this->db->get('email_queue');
        return $query->result();
    }

    public function mark_as_processing($id)
    {
        $this->db->where('id', $id);
        $this->db->update(self::TABLE_NAME, ['status' => self::STATUS_PROCESSING]);
    }

    public function mark_as_sent($id)
    {
        $this->db->where('id', $id);
        $this->db->update(self::TABLE_NAME, [
            'status' => self::STATUS_SENT,
            'processed_at' => date('Y-m-d H:i:s')
        ]);
    }

    public function mark_as_failed($id, $error_message)
    {
        $this->db->set('attempts', 'attempts+1', false);
        $this->db->where('id', $id);
        $this->db->update(self::TABLE_NAME, [
            'status' => self::STATUS_FAILED,
            'error_message' => $error_message
        ]);
    }
}
